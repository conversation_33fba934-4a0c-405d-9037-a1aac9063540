// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_CitiLaunch_1_H
#define WINRT_CitiLaunch_1_H
#include "winrt/impl/CitiLaunch.0.h"
WINRT_EXPORT namespace winrt::CitiLaunch
{
    struct __declspec(empty_bases) IBackdropBrush :
        Windows::Foundation::IInspectable,
        impl::consume_t<IBackdropBrush>
    {
        IBackdropBrush(std::nullptr_t = nullptr) noexcept {}
        IBackdropBrush(void* ptr, take_ownership_from_abi_t) noexcept : Windows::Foundation::IInspectable(ptr, take_ownership_from_abi) {}
    };
}
#endif
