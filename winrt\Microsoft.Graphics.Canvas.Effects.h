// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_Microsoft_Graphics_Canvas_Effects_H
#define WINRT_Microsoft_Graphics_Canvas_Effects_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.201201.7"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.201201.7"
#include "winrt/impl/Microsoft.Graphics.Canvas.Effects.2.h"
namespace winrt::impl
{
}
WINRT_EXPORT namespace winrt::Microsoft::Graphics::Canvas::Effects
{
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
#endif
}
#endif
