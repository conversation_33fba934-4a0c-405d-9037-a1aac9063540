# Hướng dẫn phân phối GANGHAI CITY

## Vấn đề hiện tại
Khi bạn chạy `GangHaiCity.exe` ở ngoài thư mục build, nó báo lỗi "Could not load CoreRT.dll" vì thiếu các file dependencies.

## Giải pháp

### Cách 1: Đ<PERSON>g gói đầy đủ (Khuyến nghị)
1. Chạy script `package_release.bat` để tự động sao chép tất cả file cần thiết
2. <PERSON><PERSON> thư mục `release` và gửi cho người dùng
3. Người dùng giải nén và chạy `GangHaiCity.exe`

### Cách 2: Tự động tải về (Đã sửa trong code)
1. Build lại project sau khi sửa code
2. Gửi chỉ file `GangHaiCity.exe` 
3. Lần đầu chạy sẽ hỏi người dùng có muốn tải về dữ liệu không
4. Tự động tải về các file cầ<PERSON> thiết từ server

### Cách 3: Tạo installer
1. Compile file `launcher.cpp` thành `GangHaiCityLauncher.exe`
2. Gửi launcher này cho người dùng
3. Launcher sẽ kiểm tra và tải về file cần thiết

## Files cần thiết để chạy standalone:
```
GangHaiCity.exe          (file chính)
CoreRT.dll              (core runtime)
botan.dll               (crypto library)
dinput8.dll             (input handling)
steam_api64.dll         (steam integration)
v8*.dll                 (JavaScript engine)
citizen-resources-client.dll
bin/                    (thư mục chứa các DLL phụ)
citizen/                (thư mục dữ liệu game)
```

## Cách build launcher:
```bash
g++ -o GangHaiCityLauncher.exe launcher.cpp -lwininet -mwindows
```

## Lưu ý:
- Cách 2 yêu cầu server có sẵn để tải về dữ liệu
- Cách 1 tạo package lớn nhưng chạy offline được
- Cách 3 cần setup server hosting cho các file dependencies

## Khuyến nghị:
Sử dụng Cách 1 (package_release.bat) để tạo bản phân phối đầy đủ, đảm bảo người dùng có thể chạy ngay mà không cần internet.
