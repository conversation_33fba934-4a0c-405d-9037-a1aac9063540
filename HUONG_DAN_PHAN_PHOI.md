# Hướng dẫn phân phối GANGHAI CITY

## Vấn đề hiện tại
Khi bạn chạy `GangHaiCity.exe` ở ngoài thư mục build, nó báo lỗi "Could not load CoreRT.dll" vì thiếu các file dependencies.

## KHÔNG CẦN VIẾT API RIÊNG!
Hệ thống đã có sẵn cơ chế tải về hoàn chỉnh sử dụng CURL và CDN.

## Giải pháp

### Cách 1: Sử dụng server CitizenFX (Đơn giản nhất)
- Hệ thống tự động tải từ `https://content.cfx.re/updates`
- Không cần setup gì thêm
- Code đã được sửa để tự động tải khi thiếu file

### Cách 2: Đóng gói đầy đủ (Offline)
1. Chạy script `package_release.bat` để tự động sao chép tất cả file cần thiết
2. <PERSON><PERSON> thư mụ<PERSON> `release` và gửi cho người dùng
3. Người dùng giải nén và chạy `GangHaiCity.exe`

### Cách 3: Tạo server riêng (Nếu muốn kiểm soát)
1. Chạy `python setup_update_server.py` để tạo server structure
2. Chạy `python setup_update_server.py serve` để start server
3. Sửa `CFX_UPDATER_URL` trong `LauncherConfig.h` thành URL server của bạn
4. Build lại project

## Files cần thiết để chạy standalone:
```
GangHaiCity.exe          (file chính)
CoreRT.dll              (core runtime)
botan.dll               (crypto library)
dinput8.dll             (input handling)
steam_api64.dll         (steam integration)
v8*.dll                 (JavaScript engine)
citizen-resources-client.dll
bin/                    (thư mục chứa các DLL phụ)
citizen/                (thư mục dữ liệu game)
```

## Cách build launcher:
```bash
g++ -o GangHaiCityLauncher.exe launcher.cpp -lwininet -mwindows
```

## Lưu ý:
- Cách 2 yêu cầu server có sẵn để tải về dữ liệu
- Cách 1 tạo package lớn nhưng chạy offline được
- Cách 3 cần setup server hosting cho các file dependencies

## Cách hoạt động của hệ thống tải về:
1. **Kiểm tra file:** Khi khởi động, kiểm tra `CoreRT.dll` có tồn tại không
2. **Hỏi người dùng:** Nếu thiếu, hiển thị dialog hỏi có muốn tải về không
3. **Tải về tự động:** Sử dụng `Updater_RunUpdate()` để tải từ CDN
4. **Cấu trúc file:** Files được lưu theo hash trong thư mục `citizen/`

## Server structure (nếu tự host):
```
update_server/
├── heads/fivereborn/production  (manifest file)
├── ab/cd/abcd1234...           (files theo hash)
└── ...
```

## Khuyến nghị:
- **Cho development:** Sử dụng Cách 1 (server CitizenFX) - đơn giản nhất
- **Cho production:** Sử dụng Cách 2 (package offline) - ổn định nhất
- **Cho enterprise:** Sử dụng Cách 3 (server riêng) - kiểm soát tốt nhất
