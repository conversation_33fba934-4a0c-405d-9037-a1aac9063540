@echo off
echo Đang đóng gói GANGHAI CITY...

REM Tạo thư mục release
if exist "release" rmdir /s /q "release"
mkdir "release"

REM Sao chép file executable chính
copy "GangHaiCity.exe" "release\"

REM Sao chép các DLL cần thiết
copy "CoreRT.dll" "release\" 2>nul
copy "botan.dll" "release\" 2>nul
copy "dinput8.dll" "release\" 2>nul
copy "steam_api64.dll" "release\" 2>nul
copy "v8_libplatform.dll" "release\" 2>nul
copy "v8_libbase.dll" "release\" 2>nul
copy "v8.dll" "release\" 2>nul
copy "citizen-resources-client.dll" "release\" 2>nul
copy "CitiLaunch_TLSDummy.dll" "release\" 2>nul

REM Sao chép thư mục bin
if exist "bin" (
    xcopy "bin" "release\bin\" /E /I /Y
)

REM Sao chép thư mục citizen
if exist "citizen" (
    xcopy "citizen" "release\citizen\" /E /I /Y
)

REM Sao chép các file cấu hình
copy "CitizenFX.ini" "release\" 2>nul
copy "nobootstrap.txt" "release\" 2>nul

REM Tạo file README
echo GANGHAI CITY - Hướng dẫn sử dụng > "release\README.txt"
echo. >> "release\README.txt"
echo 1. Giải nén tất cả file vào một thư mục trống >> "release\README.txt"
echo 2. Chạy GangHaiCity.exe >> "release\README.txt"
echo 3. Lần đầu chạy sẽ tự động tải về dữ liệu cần thiết >> "release\README.txt"
echo. >> "release\README.txt"
echo Lưu ý: Cần kết nối internet cho lần chạy đầu tiên >> "release\README.txt"

echo.
echo Đóng gói hoàn tất! Kiểm tra thư mục 'release'
echo Bạn có thể nén thư mục 'release' và gửi cho người khác.
pause
