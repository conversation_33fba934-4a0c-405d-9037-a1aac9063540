// WARNING: Please don't edit this file. It was generated by C++/WinRT v2.0.201201.7

#ifndef WINRT_CitiLaunch_H
#define WINRT_CitiLaunch_H
#include "winrt/base.h"
static_assert(winrt::check_version(CPPWINRT_VERSION, "2.0.201201.7"), "Mismatched C++/WinRT headers.");
#define CPPWINRT_VERSION "2.0.201201.7"
#include "winrt/impl/Windows.UI.Composition.2.h"
#include "winrt/impl/Windows.UI.Xaml.2.h"
#include "winrt/impl/Windows.UI.Xaml.Media.2.h"
#include "winrt/impl/CitiLaunch.2.h"
namespace winrt::impl
{
    template <typename D>
    struct produce<D, CitiLaunch::IBackdropBrush> : produce_base<D, CitiLaunch::IBackdropBrush>
    {
    };
}
WINRT_EXPORT namespace winrt::CitiLaunch
{
    inline BackdropBrush::BackdropBrush() :
        BackdropBrush(impl::call_factory_cast<BackdropBrush(*)(Windows::Foundation::IActivationFactory const&), BackdropBrush>([](Windows::Foundation::IActivationFactory const& f) { return f.template ActivateInstance<BackdropBrush>(); }))
    {
    }
}
namespace std
{
#ifndef WINRT_LEAN_AND_MEAN
    template<> struct hash<winrt::CitiLaunch::IBackdropBrush> : winrt::impl::hash_base {};
    template<> struct hash<winrt::CitiLaunch::BackdropBrush> : winrt::impl::hash_base {};
#endif
}
#endif
