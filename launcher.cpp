#include <windows.h>
#include <wininet.h>
#include <string>
#include <vector>
#include <iostream>

#pragma comment(lib, "wininet.lib")

class SimpleLauncher {
private:
    std::wstring exePath;
    std::vector<std::wstring> requiredFiles = {
        L"CoreRT.dll",
        L"botan.dll", 
        L"dinput8.dll",
        L"steam_api64.dll"
    };

public:
    SimpleLauncher() {
        wchar_t path[MAX_PATH];
        GetModuleFileName(NULL, path, MAX_PATH);
        std::wstring fullPath(path);
        size_t pos = fullPath.find_last_of(L"\\");
        exePath = fullPath.substr(0, pos + 1);
    }

    bool CheckRequiredFiles() {
        for (const auto& file : requiredFiles) {
            std::wstring fullPath = exePath + file;
            if (GetFileAttributes(fullPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
                return false;
            }
        }
        return true;
    }

    bool DownloadFile(const std::wstring& url, const std::wstring& localPath) {
        HINTERNET hInternet = InternetOpen(L"GangHaiCity Launcher", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
        if (!hInternet) return false;

        HINTERNET hUrl = InternetOpenUrl(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
        if (!hUrl) {
            InternetCloseHandle(hInternet);
            return false;
        }

        HANDLE hFile = CreateFile(localPath.c_str(), GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
        if (hFile == INVALID_HANDLE_VALUE) {
            InternetCloseHandle(hUrl);
            InternetCloseHandle(hInternet);
            return false;
        }

        char buffer[4096];
        DWORD bytesRead, bytesWritten;
        while (InternetReadFile(hUrl, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
            WriteFile(hFile, buffer, bytesRead, &bytesWritten, NULL);
        }

        CloseHandle(hFile);
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        return true;
    }

    void ShowMessage(const std::wstring& message, const std::wstring& title = L"GangHai City") {
        MessageBox(NULL, message.c_str(), title.c_str(), MB_OK | MB_ICONINFORMATION);
    }

    bool LaunchGame() {
        std::wstring gamePath = exePath + L"GangHaiCity.exe";
        
        STARTUPINFO si = { sizeof(si) };
        PROCESS_INFORMATION pi;
        
        if (CreateProcess(gamePath.c_str(), NULL, NULL, NULL, FALSE, 0, NULL, exePath.c_str(), &si, &pi)) {
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }
        return false;
    }

    int Run() {
        if (!CheckRequiredFiles()) {
            int result = MessageBox(NULL, 
                L"GANGHAI CITY cần tải về các file cần thiết để chạy.\nBạn có muốn tải về không?", 
                L"Tải về dữ liệu", 
                MB_YESNO | MB_ICONQUESTION);
                
            if (result == IDNO) {
                return 1;
            }

            ShowMessage(L"Đang tải về dữ liệu cần thiết...\nVui lòng đợi...");
            
            // Tại đây bạn có thể thêm logic tải về từ server
            // Hoặc chỉ hiển thị thông báo hướng dẫn
            ShowMessage(L"Vui lòng tải về bản đầy đủ từ website chính thức.");
            return 1;
        }

        if (!LaunchGame()) {
            ShowMessage(L"Không thể khởi chạy game!", L"Lỗi");
            return 1;
        }

        return 0;
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    SimpleLauncher launcher;
    return launcher.Run();
}
