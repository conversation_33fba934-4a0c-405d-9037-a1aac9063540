#include "StdInc.h"
#include "GangHaiCityDownloader.h"
#include <json/json.h>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <openssl/sha.h>

GangHaiCityDownloader::GangHaiCityDownloader(const std::string& baseUrl) 
    : serverUrl(baseUrl) {
}

void GangHaiCityDownloader::SetProgressCallback(ProgressCallback callback) {
    progressCallback = callback;
}

std::string GangHaiCityDownloader::HttpGet(const std::string& url) {
    HINTERNET hInternet = InternetOpenA("GangHaiCity Downloader", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return "";

    HINTERNET hUrl = InternetOpenUrlA(hInternet, url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hUrl) {
        InternetCloseHandle(hInternet);
        return "";
    }

    std::string result;
    char buffer[4096];
    DWORD bytesRead;
    
    while (InternetReadFile(hUrl, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
        result.append(buffer, bytesRead);
    }

    InternetCloseHandle(hUrl);
    InternetCloseHandle(hInternet);
    return result;
}

bool GangHaiCityDownloader::FetchFileList() {
    std::string url = serverUrl + "/api/files";
    std::string response = HttpGet(url);
    
    if (response.empty()) {
        return false;
    }

    try {
        Json::Value root;
        Json::Reader reader;
        
        if (!reader.parse(response, root)) {
            return false;
        }

        requiredFiles.clear();
        const Json::Value& files = root["files"];
        
        for (const auto& file : files) {
            FileInfo info;
            info.filename = file["filename"].asString();
            info.url = serverUrl + "/api/download/" + info.filename;
            info.size = file["size"].asUInt64();
            info.hash = file["hash"].asString();
            requiredFiles.push_back(info);
        }
        
        return true;
    }
    catch (...) {
        return false;
    }
}

std::string GangHaiCityDownloader::CalculateFileHash(const std::string& filePath) {
    std::ifstream file(filePath, std::ios::binary);
    if (!file) return "";

    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    
    char buffer[4096];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        SHA256_Update(&sha256, buffer, file.gcount());
    }
    
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_Final(hash, &sha256);
    
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    
    return ss.str();
}

bool GangHaiCityDownloader::VerifyFile(const std::string& filePath, const std::string& expectedHash) {
    if (GetFileAttributesA(filePath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        return false;
    }
    
    std::string actualHash = CalculateFileHash(filePath);
    return actualHash == expectedHash;
}

bool GangHaiCityDownloader::DownloadFile(const FileInfo& fileInfo, const std::string& localPath) {
    HINTERNET hInternet = InternetOpenA("GangHaiCity Downloader", INTERNET_OPEN_TYPE_DIRECT, NULL, NULL, 0);
    if (!hInternet) return false;

    HINTERNET hUrl = InternetOpenUrlA(hInternet, fileInfo.url.c_str(), NULL, 0, INTERNET_FLAG_RELOAD, 0);
    if (!hUrl) {
        InternetCloseHandle(hInternet);
        return false;
    }

    HANDLE hFile = CreateFileA(localPath.c_str(), GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        InternetCloseHandle(hUrl);
        InternetCloseHandle(hInternet);
        return false;
    }

    char buffer[4096];
    DWORD bytesRead, bytesWritten;
    size_t totalDownloaded = 0;
    
    while (InternetReadFile(hUrl, buffer, sizeof(buffer), &bytesRead) && bytesRead > 0) {
        WriteFile(hFile, buffer, bytesRead, &bytesWritten, NULL);
        totalDownloaded += bytesRead;
        
        if (progressCallback && fileInfo.size > 0) {
            int percentage = (int)((totalDownloaded * 100) / fileInfo.size);
            progressCallback(percentage, fileInfo.filename);
        }
    }

    CloseHandle(hFile);
    InternetCloseHandle(hUrl);
    InternetCloseHandle(hInternet);
    
    // Verify downloaded file
    return VerifyFile(localPath, fileInfo.hash);
}

bool GangHaiCityDownloader::DownloadAllFiles() {
    if (!FetchFileList()) {
        return false;
    }

    UI_DoCreation();
    UI_UpdateText(0, L"Đang tải về GANGHAI CITY...");
    
    int totalFiles = requiredFiles.size();
    int currentFile = 0;
    
    for (const auto& fileInfo : requiredFiles) {
        currentFile++;
        
        std::string localPath = fileInfo.filename;
        
        // Skip if file already exists and is valid
        if (VerifyFile(localPath, fileInfo.hash)) {
            continue;
        }
        
        std::wstring statusText = L"Đang tải: " + std::wstring(fileInfo.filename.begin(), fileInfo.filename.end()) + 
                                 L" (" + std::to_wstring(currentFile) + L"/" + std::to_wstring(totalFiles) + L")";
        UI_UpdateText(0, statusText.c_str());
        
        if (!DownloadFile(fileInfo, localPath)) {
            UI_DoDestruction();
            return false;
        }
        
        int overallProgress = (currentFile * 100) / totalFiles;
        UI_UpdateProgress(overallProgress);
    }
    
    UI_DoDestruction();
    return true;
}

// Function để gọi từ Bootstrap.cpp
bool DownloadGangHaiCityFiles() {
    GangHaiCityDownloader downloader("http://localhost:3000");
    return downloader.DownloadAllFiles();
}
