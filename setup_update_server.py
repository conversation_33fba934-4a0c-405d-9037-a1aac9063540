#!/usr/bin/env python3
"""
Script đơn giản để tạo update server cho GANGHAI CITY
Chỉ cần host các file static, không cần API phức tạp
"""

import os
import shutil
import hashlib
import json
from pathlib import Path
import http.server
import socketserver

class UpdateServerSetup:
    def __init__(self, source_dir=".", output_dir="update_server"):
        self.source_dir = Path(source_dir)
        self.output_dir = Path(output_dir)
        self.files_to_serve = [
            "CoreRT.dll",
            "botan.dll", 
            "dinput8.dll",
            "steam_api64.dll",
            "v8_libplatform.dll",
            "v8_libbase.dll", 
            "v8.dll",
            "citizen-resources-client.dll"
        ]
    
    def calculate_hash(self, file_path):
        """Tính SHA256 hash của file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    def setup_directory_structure(self):
        """Tạo cấu trúc thư mục cho update server"""
        self.output_dir.mkdir(exist_ok=True)
        (self.output_dir / "heads").mkdir(exist_ok=True)
        (self.output_dir / "heads" / "fivereborn").mkdir(exist_ok=True)
        
        # Tạo thư mục cho files theo hash (2 ký tự đầu)
        for i in range(256):
            hex_dir = f"{i:02x}"
            (self.output_dir / hex_dir).mkdir(exist_ok=True)
            for j in range(256):
                sub_hex_dir = f"{j:02x}"
                (self.output_dir / hex_dir / sub_hex_dir).mkdir(exist_ok=True)
    
    def copy_files_by_hash(self):
        """Sao chép files theo cấu trúc hash"""
        manifest_files = []
        
        for file_name in self.files_to_serve:
            source_file = self.source_dir / file_name
            if source_file.exists():
                file_hash = self.calculate_hash(source_file)
                file_size = source_file.stat().st_size
                
                # Tạo đường dẫn theo hash
                hash_dir = file_hash[:2]
                sub_hash_dir = file_hash[2:4]
                dest_dir = self.output_dir / hash_dir / sub_hash_dir
                dest_file = dest_dir / file_hash
                
                # Sao chép file
                shutil.copy2(source_file, dest_file)
                
                manifest_files.append({
                    "name": file_name,
                    "hash": file_hash,
                    "size": file_size
                })
                
                print(f"Copied {file_name} -> {file_hash}")
        
        return manifest_files
    
    def create_manifest(self, manifest_files):
        """Tạo manifest file"""
        manifest = {
            "version": 1,
            "files": manifest_files
        }
        
        manifest_path = self.output_dir / "heads" / "fivereborn" / "production"
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print(f"Created manifest: {manifest_path}")
    
    def setup_server(self):
        """Setup toàn bộ server"""
        print("Setting up GANGHAI CITY update server...")
        
        self.setup_directory_structure()
        manifest_files = self.copy_files_by_hash()
        self.create_manifest(manifest_files)
        
        print(f"\nServer setup complete in: {self.output_dir}")
        print("To start server, run:")
        print(f"cd {self.output_dir} && python -m http.server 8080")
        print("\nThen update CFX_UPDATER_URL to: http://localhost:8080")

def start_simple_server(port=8080, directory="update_server"):
    """Khởi động HTTP server đơn giản"""
    os.chdir(directory)
    handler = http.server.SimpleHTTPRequestHandler
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        print(f"Server running at http://localhost:{port}")
        print("Press Ctrl+C to stop")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "serve":
        start_simple_server()
    else:
        setup = UpdateServerSetup()
        setup.setup_server()
        
        print("\nTo start serving files:")
        print("python setup_update_server.py serve")
